import git
from git import Repo, GitCommandError
from typing import List, Dict, Any

class GitService:
    def __init__(self, repo_path: str = "."):
        try:
            self.repo = Repo(repo_path, search_parent_directories=True)
        except git.InvalidGitRepositoryError:
            self.repo = None

    def get_status(self) -> Dict[str, Any]:
        if not self.repo:
            return {"error": "Not a git repository"}
        
        return {
            "branch": self.repo.active_branch.name,
            "untracked_files": self.repo.untracked_files,
            "changed_files": [item.a_path for item in self.repo.index.diff(None)],
            "new_files": [item.a_path for item in self.repo.index.diff(None).iter_change_type('A')],
        }

    def get_current_branch(self) -> Dict[str, str]:
        if not self.repo:
            return {"error": "Not a git repository"}
        return {"branch": self.repo.active_branch.name}

    def get_commit_history(self, max_count: int = 10) -> List[Dict[str, Any]]:
        if not self.repo:
            return []
        
        commits = list(self.repo.iter_commits(max_count=max_count))
        return [
            {
                "hexsha": commit.hexsha,
                "message": commit.message.strip(),
                "author": commit.author.name,
                "date": commit.committed_datetime.isoformat(),
            }
            for commit in commits
        ]
