from fastapi import APIRouter, Depends
from typing import List, Dict, Any
from backend.services.git_service import GitService

router = APIRouter()

def get_git_service():
    return GitService()

@router.get("/status", response_model=Dict[str, Any])
def get_status(service: GitService = Depends(get_git_service)):
    return service.get_status()

@router.get("/branch", response_model=Dict[str, str])
def get_current_branch(service: GitService = Depends(get_git_service)):
    return service.get_current_branch()

@router.get("/commits", response_model=List[Dict[str, Any]])
def get_commit_history(service: GitService = Depends(get_git_service)):
    return service.get_commit_history()
